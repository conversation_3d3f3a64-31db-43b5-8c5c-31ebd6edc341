# WebGL中国地图

一个使用WebGL技术实现的交互式中国地图，支持省份高亮、缩放、平移等功能。

## 功能特性

### 🗺️ 地图显示
- 使用WebGL渲染，性能优异
- 支持中国地图的完整显示
- 自适应屏幕大小
- 流畅的动画效果

### 🖱️ 交互功能
- **鼠标拖拽**：平移地图
- **滚轮缩放**：放大/缩小地图
- **双击重置**：恢复初始视图
- **省份高亮**：鼠标悬停时高亮显示省份
- **省份名称**：实时显示鼠标悬停的省份名称

### 🎨 视觉效果
- 多种地图颜色主题（蓝色、绿色、红色、紫色、橙色、黄色）
- 线框模式切换
- 省份边界线显示
- 渐变色彩效果
- 微妙的动画脉冲效果

### ⚡ 性能优化
- WebGL硬件加速渲染
- 实时FPS显示
- 高效的缓冲区管理
- 优化的省份检测算法

## 文件结构

```
├── webgl-china-map.html    # 主HTML文件
├── webgl-map-engine.js     # WebGL渲染引擎
├── china-map-data.js       # 地图数据处理器
├── main.js                 # 主程序逻辑
└── README.md              # 说明文档
```

## 使用方法

1. **直接打开**：在浏览器中打开 `webgl-china-map.html` 文件
2. **本地服务器**：推荐使用本地HTTP服务器运行，避免跨域问题

### 控制面板
- **缩放级别**：使用滑块调整地图缩放
- **地图颜色**：点击按钮切换不同颜色主题
- **重置视图**：恢复地图到初始状态
- **线框模式**：切换到线框显示模式
- **动画效果**：开启/关闭动画效果

### 快捷键
- `R` - 重置视图
- `W` - 切换线框模式
- `A` - 切换动画效果
- `1-6` - 切换不同颜色主题

## 技术实现

### WebGL渲染引擎
- 使用原生WebGL API
- 自定义顶点和片段着色器
- 支持矩阵变换和投影
- 优化的缓冲区管理

### 地图数据处理
- GeoJSON格式支持
- 坐标投影转换
- 多边形三角剖分
- 省份边界检测

### 交互系统
- 鼠标和触摸事件处理
- 坐标系转换
- 射线投射算法进行省份检测
- 实时状态更新

## 浏览器兼容性

- ✅ Chrome 50+
- ✅ Firefox 45+
- ✅ Safari 10+
- ✅ Edge 12+
- ⚠️ IE 11（部分功能可能不支持）

## 数据来源

- 内置简化的中国地图数据
- 支持加载外部GeoJSON数据
- 自动降级到内置数据

## 开发说明

### 添加新省份数据
在 `china-map-data.js` 的 `getBuiltinChinaData()` 方法中添加新的省份数据：

```javascript
{
    "type": "Feature",
    "properties": {
        "name": "省份名称"
    },
    "geometry": {
        "type": "Polygon",
        "coordinates": [[
            [经度1, 纬度1],
            [经度2, 纬度2],
            // ... 更多坐标点
        ]]
    }
}
```

### 自定义颜色主题
在 `main.js` 的 `changeMapColor()` 函数中添加新的颜色：

```javascript
const colors = {
    'custom': [r, g, b, a], // RGBA值，范围0-1
    // ... 其他颜色
};
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
