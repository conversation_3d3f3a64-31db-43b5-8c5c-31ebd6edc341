/**
 * 中国地图数据处理器
 */
class ChinaMapData {
    constructor() {
        this.geoData = null;
        this.vertices = [];
        this.indices = [];
        this.borderVertices = [];
        this.provinces = [];
    }
    
    // 加载GeoJSON数据
    async loadGeoJSON(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.geoData = await response.json();
            console.log('GeoJSON数据加载成功');
            return this.geoData;
        } catch (error) {
            console.error('加载GeoJSON数据失败:', error);
            // 如果无法加载外部数据，使用内置的简化数据
            this.geoData = this.getBuiltinChinaData();
            return this.geoData;
        }
    }
    
    // 内置的简化中国地图数据
    getBuiltinChinaData() {
        return {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "中国"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [73.66, 39.55], [78.12, 39.58], [80.25, 40.95], [82.31, 42.12],
                            [86.15, 42.89], [88.17, 43.97], [90.28, 45.84], [93.48, 46.75],
                            [95.31, 48.52], [97.45, 49.72], [99.52, 50.33], [101.83, 50.42],
                            [104.62, 50.27], [106.12, 50.27], [107.95, 49.81], [109.47, 49.13],
                            [111.95, 49.37], [114.46, 50.25], [116.68, 49.93], [118.06, 49.05],
                            [119.78, 47.05], [121.68, 46.75], [123.20, 46.27], [125.68, 45.61],
                            [127.05, 44.78], [128.78, 44.37], [130.78, 42.28], [131.18, 41.33],
                            [131.29, 40.06], [130.55, 38.42], [130.68, 37.09], [131.89, 35.32],
                            [132.38, 34.07], [131.29, 32.06], [121.50, 25.30], [121.46, 21.55],
                            [111.95, 21.23], [108.05, 21.55], [106.55, 21.23], [108.32, 18.85],
                            [108.05, 12.99], [111.67, 10.44], [109.47, 7.35], [108.65, 6.93],
                            [117.41, 7.97], [119.78, 10.44], [119.78, 15.75], [117.41, 23.26],
                            [114.46, 22.20], [114.31, 22.58], [113.80, 22.54], [113.54, 22.54],
                            [109.99, 21.30], [108.32, 21.64], [106.81, 21.64], [105.21, 23.14],
                            [103.75, 22.54], [102.27, 22.37], [101.74, 21.17], [101.74, 21.17],
                            [99.52, 22.11], [97.45, 23.90], [97.30, 24.47], [98.67, 24.47],
                            [98.67, 26.30], [98.67, 27.78], [98.82, 28.18], [98.82, 28.98],
                            [99.52, 28.98], [99.52, 29.11], [100.13, 28.88], [100.13, 28.25],
                            [101.74, 27.88], [101.74, 27.05], [102.27, 26.30], [102.27, 25.30],
                            [103.75, 24.47], [105.21, 23.14], [106.81, 21.64], [108.32, 21.64],
                            [109.99, 21.30], [113.54, 22.54], [113.80, 22.54], [114.31, 22.58],
                            [114.46, 22.20], [117.41, 23.26], [119.78, 15.75], [119.78, 10.44],
                            [117.41, 7.97], [108.65, 6.93], [109.47, 7.35], [111.67, 10.44],
                            [108.05, 12.99], [108.32, 18.85], [106.55, 21.23], [108.05, 21.55],
                            [111.95, 21.23], [121.46, 21.55], [121.50, 25.30], [131.29, 32.06],
                            [132.38, 34.07], [131.89, 35.32], [130.68, 37.09], [130.55, 38.42],
                            [131.29, 40.06], [131.18, 41.33], [130.78, 42.28], [128.78, 44.37],
                            [127.05, 44.78], [125.68, 45.61], [123.20, 46.27], [121.68, 46.75],
                            [119.78, 47.05], [118.06, 49.05], [116.68, 49.93], [114.46, 50.25],
                            [111.95, 49.37], [109.47, 49.13], [107.95, 49.81], [106.12, 50.27],
                            [104.62, 50.27], [101.83, 50.42], [99.52, 50.33], [97.45, 49.72],
                            [95.31, 48.52], [93.48, 46.75], [90.28, 45.84], [88.17, 43.97],
                            [86.15, 42.89], [82.31, 42.12], [80.25, 40.95], [78.12, 39.58],
                            [73.66, 39.55]
                        ]]
                    }
                }
            ]
        };
    }
    
    // 处理GeoJSON数据，转换为WebGL顶点数据
    processGeoData() {
        if (!this.geoData) {
            console.error('没有可用的地图数据');
            return;
        }
        
        this.vertices = [];
        this.indices = [];
        this.borderVertices = [];
        this.provinces = [];
        
        let vertexIndex = 0;
        
        this.geoData.features.forEach((feature, featureIndex) => {
            const geometry = feature.geometry;
            const properties = feature.properties;
            
            if (geometry.type === 'Polygon') {
                this.processPolygon(geometry.coordinates, properties, vertexIndex);
            } else if (geometry.type === 'MultiPolygon') {
                geometry.coordinates.forEach(polygon => {
                    this.processPolygon(polygon, properties, vertexIndex);
                });
            }
        });
        
        console.log('地图数据处理完成');
        console.log('顶点数:', this.vertices.length / 2);
        console.log('索引数:', this.indices.length);
        console.log('省份数:', this.provinces.length);
    }
    
    // 处理多边形数据
    processPolygon(coordinates, properties, startIndex) {
        coordinates.forEach((ring, ringIndex) => {
            if (ringIndex === 0) { // 外环
                const triangles = this.triangulatePolygon(ring);

                // 添加顶点
                ring.forEach(coord => {
                    const [lon, lat] = this.projectCoordinate(coord[0], coord[1]);
                    this.vertices.push(lon, lat);
                });

                // 添加边界线顶点
                for (let i = 0; i < ring.length - 1; i++) {
                    const [lon1, lat1] = this.projectCoordinate(ring[i][0], ring[i][1]);
                    const [lon2, lat2] = this.projectCoordinate(ring[i + 1][0], ring[i + 1][1]);
                    this.borderVertices.push(lon1, lat1, lon2, lat2);
                }

                // 添加三角形索引
                triangles.forEach(triangle => {
                    this.indices.push(
                        startIndex + triangle[0],
                        startIndex + triangle[1],
                        startIndex + triangle[2]
                    );
                });

                // 记录省份信息
                this.provinces.push({
                    name: properties.name || '未知',
                    startVertex: startIndex,
                    vertexCount: ring.length,
                    startIndex: this.indices.length - triangles.length * 3,
                    indexCount: triangles.length * 3
                });

                startIndex += ring.length;
            }
        });
    }
    
    // 坐标投影（经纬度转换为屏幕坐标）
    projectCoordinate(lon, lat) {
        // 简单的墨卡托投影
        const x = (lon - 104) / 30; // 中国中心经度约104度
        const y = (lat - 35) / 15;  // 中国中心纬度约35度
        return [x, y];
    }
    
    // 简单的多边形三角剖分（耳切法的简化版本）
    triangulatePolygon(ring) {
        const triangles = [];
        const vertices = ring.slice(0, -1); // 移除重复的最后一个点
        
        if (vertices.length < 3) return triangles;
        
        // 简单的扇形三角剖分
        for (let i = 1; i < vertices.length - 1; i++) {
            triangles.push([0, i, i + 1]);
        }
        
        return triangles;
    }
    
    // 获取处理后的顶点数据
    getVertices() {
        return this.vertices;
    }
    
    // 获取索引数据
    getIndices() {
        return this.indices;
    }

    // 获取边界线顶点数据
    getBorderVertices() {
        return this.borderVertices;
    }
    
    // 获取省份信息
    getProvinces() {
        return this.provinces;
    }
    
    // 获取地图边界
    getBounds() {
        if (this.vertices.length === 0) return null;
        
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        
        for (let i = 0; i < this.vertices.length; i += 2) {
            const x = this.vertices[i];
            const y = this.vertices[i + 1];
            
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
        }
        
        return { minX, maxX, minY, maxY };
    }
}
