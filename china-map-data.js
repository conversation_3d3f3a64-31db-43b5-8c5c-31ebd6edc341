/**
 * 中国地图数据处理器
 */
class ChinaMapData {
    constructor() {
        this.geoData = null;
        this.vertices = [];
        this.indices = [];
        this.borderVertices = [];
        this.provinces = [];
        this.provinceBuffers = []; // 每个省份的独立缓冲区数据
    }
    
    // 加载GeoJSON数据
    async loadGeoJSON(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.geoData = await response.json();
            console.log('GeoJSON数据加载成功');
            return this.geoData;
        } catch (error) {
            console.error('加载GeoJSON数据失败:', error);
            // 如果无法加载外部数据，使用内置的简化数据
            this.geoData = this.getBuiltinChinaData();
            return this.geoData;
        }
    }
    
    // 内置的简化中国地图数据（包含主要省份）
    getBuiltinChinaData() {
        return {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "新疆维吾尔自治区"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [73.66, 39.55], [78.12, 39.58], [80.25, 40.95], [82.31, 42.12],
                            [86.15, 42.89], [88.17, 43.97], [90.28, 45.84], [93.48, 46.75],
                            [95.31, 48.52], [97.45, 49.72], [87.45, 47.72], [82.45, 45.72],
                            [78.45, 43.72], [75.45, 41.72], [73.66, 39.55]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "内蒙古自治区"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [97.45, 49.72], [99.52, 50.33], [101.83, 50.42], [104.62, 50.27],
                            [106.12, 50.27], [107.95, 49.81], [109.47, 49.13], [111.95, 49.37],
                            [114.46, 50.25], [116.68, 49.93], [118.06, 49.05], [119.78, 47.05],
                            [121.68, 46.75], [123.20, 46.27], [125.68, 45.61], [127.05, 44.78],
                            [125.05, 42.78], [120.05, 43.78], [115.05, 44.78], [110.05, 45.78],
                            [105.05, 46.78], [100.05, 47.78], [97.45, 49.72]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "黑龙江省"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [125.68, 45.61], [127.05, 44.78], [128.78, 44.37], [130.78, 42.28],
                            [131.18, 41.33], [133.18, 43.33], [134.18, 45.33], [133.18, 47.33],
                            [131.18, 48.33], [129.18, 49.33], [127.18, 48.33], [125.68, 45.61]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "广东省"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [109.99, 21.30], [113.54, 22.54], [113.80, 22.54], [114.31, 22.58],
                            [114.46, 22.20], [117.41, 23.26], [119.78, 20.75], [118.78, 19.75],
                            [116.78, 20.75], [114.78, 21.75], [112.78, 22.75], [110.78, 21.75],
                            [109.99, 21.30]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "四川省"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [97.30, 24.47], [98.67, 24.47], [98.67, 26.30], [98.67, 27.78],
                            [98.82, 28.18], [98.82, 28.98], [99.52, 28.98], [99.52, 29.11],
                            [100.13, 28.88], [100.13, 28.25], [101.74, 27.88], [101.74, 27.05],
                            [102.27, 26.30], [104.27, 28.30], [106.27, 30.30], [108.27, 32.30],
                            [106.27, 33.30], [104.27, 32.30], [102.27, 31.30], [100.27, 30.30],
                            [98.27, 29.30], [97.30, 24.47]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "北京市"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [115.7, 39.4], [117.4, 39.4], [117.4, 41.6], [115.7, 41.6], [115.7, 39.4]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "上海市"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [120.9, 30.7], [122.2, 30.7], [122.2, 31.9], [120.9, 31.9], [120.9, 30.7]
                        ]]
                    }
                }
            ]
        };
    }
    
    // 处理GeoJSON数据，转换为WebGL顶点数据
    processGeoData() {
        if (!this.geoData) {
            console.error('没有可用的地图数据');
            return;
        }
        
        this.vertices = [];
        this.indices = [];
        this.borderVertices = [];
        this.provinces = [];
        this.provinceBuffers = [];
        
        let vertexIndex = 0;
        
        this.geoData.features.forEach((feature, index) => {
            const geometry = feature.geometry;
            const properties = feature.properties;

            // 为每个省份创建独立的缓冲区数据
            const provinceData = {
                id: index,
                name: properties.name || properties.NAME || properties.NAME_CHN || `省份${index}`,
                vertices: [],
                indices: [],
                borderVertices: [],
                bounds: { minX: Infinity, maxX: -Infinity, minY: Infinity, maxY: -Infinity }
            };

            if (geometry.type === 'Polygon') {
                this.processPolygonForProvince(geometry.coordinates, provinceData, vertexIndex);
            } else if (geometry.type === 'MultiPolygon') {
                geometry.coordinates.forEach(polygon => {
                    this.processPolygonForProvince(polygon, provinceData, vertexIndex);
                });
            }

            this.provinceBuffers.push(provinceData);

            // 同时添加到全局数据中
            this.vertices.push(...provinceData.vertices);
            this.indices.push(...provinceData.indices.map(i => i + vertexIndex));
            this.borderVertices.push(...provinceData.borderVertices);

            vertexIndex += provinceData.vertices.length / 2;
        });
        
        console.log('地图数据处理完成');
        console.log('顶点数:', this.vertices.length / 2);
        console.log('索引数:', this.indices.length);
        console.log('省份数:', this.provinces.length);
    }
    
    // 为省份处理多边形数据
    processPolygonForProvince(coordinates, provinceData, globalStartIndex) {
        coordinates.forEach((ring, ringIndex) => {
            if (ringIndex === 0) { // 外环
                const startIndex = provinceData.vertices.length / 2;
                const triangles = this.triangulatePolygon(ring);

                // 添加顶点到省份数据
                ring.forEach(coord => {
                    const [lon, lat] = this.projectCoordinate(coord[0], coord[1]);
                    provinceData.vertices.push(lon, lat);

                    // 更新边界
                    provinceData.bounds.minX = Math.min(provinceData.bounds.minX, lon);
                    provinceData.bounds.maxX = Math.max(provinceData.bounds.maxX, lon);
                    provinceData.bounds.minY = Math.min(provinceData.bounds.minY, lat);
                    provinceData.bounds.maxY = Math.max(provinceData.bounds.maxY, lat);
                });

                // 添加边界线顶点到省份数据
                for (let i = 0; i < ring.length - 1; i++) {
                    const [lon1, lat1] = this.projectCoordinate(ring[i][0], ring[i][1]);
                    const [lon2, lat2] = this.projectCoordinate(ring[i + 1][0], ring[i + 1][1]);
                    provinceData.borderVertices.push(lon1, lat1, lon2, lat2);
                }

                // 添加三角形索引到省份数据
                triangles.forEach(triangle => {
                    provinceData.indices.push(
                        startIndex + triangle[0],
                        startIndex + triangle[1],
                        startIndex + triangle[2]
                    );
                });

                // 记录省份信息
                this.provinces.push({
                    id: provinceData.id,
                    name: provinceData.name,
                    startVertex: globalStartIndex + startIndex,
                    vertexCount: ring.length,
                    startIndex: this.indices.length,
                    indexCount: triangles.length * 3,
                    bounds: { ...provinceData.bounds }
                });
            }
        });
    }

    // 处理多边形数据（保留原函数用于兼容）
    processPolygon(coordinates, properties, startIndex) {
        coordinates.forEach((ring, ringIndex) => {
            if (ringIndex === 0) { // 外环
                const triangles = this.triangulatePolygon(ring);

                // 添加顶点
                ring.forEach(coord => {
                    const [lon, lat] = this.projectCoordinate(coord[0], coord[1]);
                    this.vertices.push(lon, lat);
                });

                // 添加边界线顶点
                for (let i = 0; i < ring.length - 1; i++) {
                    const [lon1, lat1] = this.projectCoordinate(ring[i][0], ring[i][1]);
                    const [lon2, lat2] = this.projectCoordinate(ring[i + 1][0], ring[i + 1][1]);
                    this.borderVertices.push(lon1, lat1, lon2, lat2);
                }

                // 添加三角形索引
                triangles.forEach(triangle => {
                    this.indices.push(
                        startIndex + triangle[0],
                        startIndex + triangle[1],
                        startIndex + triangle[2]
                    );
                });

                // 记录省份信息
                this.provinces.push({
                    name: properties.name || '未知',
                    startVertex: startIndex,
                    vertexCount: ring.length,
                    startIndex: this.indices.length - triangles.length * 3,
                    indexCount: triangles.length * 3
                });

                startIndex += ring.length;
            }
        });
    }
    
    // 坐标投影（经纬度转换为屏幕坐标）
    projectCoordinate(lon, lat) {
        // 改进的投影，适合中国地图显示
        // 中国地理范围：经度73°-135°，纬度18°-54°
        const centerLon = 104; // 中国中心经度
        const centerLat = 36;  // 中国中心纬度

        // 调整投影比例，使地图更好地适应屏幕
        const lonRange = 62; // 135 - 73
        const latRange = 36; // 54 - 18

        const x = (lon - centerLon) / (lonRange * 0.6); // 缩小经度范围
        const y = (lat - centerLat) / (latRange * 0.6); // 缩小纬度范围

        return [x, y];
    }
    
    // 简单的多边形三角剖分（耳切法的简化版本）
    triangulatePolygon(ring) {
        const triangles = [];
        const vertices = ring.slice(0, -1); // 移除重复的最后一个点
        
        if (vertices.length < 3) return triangles;
        
        // 简单的扇形三角剖分
        for (let i = 1; i < vertices.length - 1; i++) {
            triangles.push([0, i, i + 1]);
        }
        
        return triangles;
    }
    
    // 获取处理后的顶点数据
    getVertices() {
        return this.vertices;
    }
    
    // 获取索引数据
    getIndices() {
        return this.indices;
    }

    // 获取边界线顶点数据
    getBorderVertices() {
        return this.borderVertices;
    }
    
    // 获取省份信息
    getProvinces() {
        return this.provinces;
    }

    // 获取省份缓冲区数据
    getProvinceBuffers() {
        return this.provinceBuffers;
    }

    // 根据坐标查找省份
    findProvinceByCoordinate(x, y) {
        for (const province of this.provinceBuffers) {
            if (this.isPointInProvince(x, y, province)) {
                return province;
            }
        }
        return null;
    }

    // 检查点是否在省份内（射线投射算法）
    isPointInProvince(x, y, province) {
        // 首先进行边界框检查
        const bounds = province.bounds;
        if (x < bounds.minX || x > bounds.maxX || y < bounds.minY || y > bounds.maxY) {
            return false;
        }

        // 使用射线投射算法进行精确检测
        const vertices = province.vertices;
        let inside = false;

        for (let i = 0, j = vertices.length - 2; i < vertices.length; j = i, i += 2) {
            const xi = vertices[i];
            const yi = vertices[i + 1];
            const xj = vertices[j];
            const yj = vertices[j + 1];

            if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
        }

        return inside;
    }
    
    // 获取地图边界
    getBounds() {
        if (this.vertices.length === 0) return null;
        
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        
        for (let i = 0; i < this.vertices.length; i += 2) {
            const x = this.vertices[i];
            const y = this.vertices[i + 1];
            
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
        }
        
        return { minX, maxX, minY, maxY };
    }
}
