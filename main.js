/**
 * WebGL中国地图主程序
 */

let mapEngine = null;
let mapData = null;

// 初始化应用
async function initApp() {
    try {
        // 获取画布元素
        const canvas = document.getElementById('mapCanvas');
        if (!canvas) {
            throw new Error('找不到画布元素');
        }
        
        // 设置画布大小
        resizeCanvas(canvas);
        
        // 创建WebGL地图引擎
        mapEngine = new WebGLMapEngine(canvas);
        
        // 创建地图数据处理器
        mapData = new ChinaMapData();
        
        // 加载地图数据
        showLoading(true);
        await loadMapData();
        
        // 初始化控件
        initControls();
        
        // 隐藏加载界面
        showLoading(false);
        
        console.log('应用初始化完成');
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        showError('应用初始化失败: ' + error.message);
    }
}

// 加载地图数据
async function loadMapData() {
    try {
        // 尝试加载在线GeoJSON数据
        const geoJsonUrls = [
            'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json', // 阿里云数据
            'https://raw.githubusercontent.com/hujiaweibujidao/china-geojson/master/china.json' // GitHub数据
        ];
        
        let loaded = false;
        for (const url of geoJsonUrls) {
            try {
                await mapData.loadGeoJSON(url);
                loaded = true;
                break;
            } catch (error) {
                console.warn('加载数据源失败:', url, error);
                continue;
            }
        }
        
        if (!loaded) {
            console.log('使用内置地图数据');
            mapData.loadGeoJSON(); // 使用内置数据
        }
        
        // 处理地图数据
        mapData.processGeoData();
        
        // 加载到WebGL引擎
        const vertices = mapData.getVertices();
        const indices = mapData.getIndices();
        const borderVertices = mapData.getBorderVertices();

        if (vertices.length === 0) {
            throw new Error('地图数据为空');
        }

        mapEngine.loadMapData(vertices, indices, borderVertices, mapData);
        
    } catch (error) {
        console.error('加载地图数据失败:', error);
        throw error;
    }
}

// 初始化控件
function initControls() {
    // 缩放滑块
    const zoomSlider = document.getElementById('zoomSlider');
    const zoomValue = document.getElementById('zoomValue');
    
    if (zoomSlider && zoomValue) {
        zoomSlider.addEventListener('input', (e) => {
            const scale = parseFloat(e.target.value);
            mapEngine.scale = scale;
            zoomValue.textContent = scale.toFixed(1);
        });
    }
    
    // 键盘事件
    document.addEventListener('keydown', (e) => {
        switch (e.key) {
            case 'r':
            case 'R':
                resetView();
                break;
            case 'w':
            case 'W':
                toggleWireframe();
                break;
            case '1':
                changeMapColor('blue');
                break;
            case '2':
                changeMapColor('green');
                break;
            case '3':
                changeMapColor('red');
                break;
            case '4':
                changeMapColor('purple');
                break;
            case '5':
                changeMapColor('orange');
                break;
            case '6':
                changeMapColor('yellow');
                break;
            case 'a':
            case 'A':
                toggleAnimation();
                break;
        }
    });
}

// 调整画布大小
function resizeCanvas(canvas) {
    const container = document.getElementById('container');
    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;
}

// 显示/隐藏加载界面
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}

// 显示错误信息
function showError(message) {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.innerHTML = `
            <div style="color: #ff6b6b; text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>${message}</div>
                <button onclick="location.reload()" style="margin-top: 15px; padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    重新加载
                </button>
            </div>
        `;
    }
}

// 重置视图
function resetView() {
    if (mapEngine) {
        mapEngine.resetView();
    }
}

// 切换线框模式
function toggleWireframe() {
    if (mapEngine) {
        mapEngine.toggleWireframe();
    }
}

// 切换动画效果
function toggleAnimation() {
    if (mapEngine) {
        mapEngine.toggleAnimation();
    }
}

// 改变地图颜色
function changeMapColor(colorName) {
    if (!mapEngine) return;
    
    const colors = {
        'blue': [0.2, 0.6, 1.0, 1.0],
        'green': [0.2, 0.8, 0.2, 1.0],
        'red': [0.8, 0.2, 0.2, 1.0],
        'purple': [0.6, 0.2, 0.8, 1.0],
        'orange': [1.0, 0.6, 0.2, 1.0],
        'yellow': [0.9, 0.9, 0.2, 1.0]
    };
    
    const color = colors[colorName] || colors['blue'];
    mapEngine.setMapColor(color);
}

// 窗口大小变化处理
window.addEventListener('resize', () => {
    if (mapEngine) {
        const canvas = document.getElementById('mapCanvas');
        resizeCanvas(canvas);
        mapEngine.resizeCanvas();
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (mapEngine) {
        mapEngine.destroy();
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    initApp();
});

// 导出全局函数供HTML调用
window.resetView = resetView;
window.toggleWireframe = toggleWireframe;
window.toggleAnimation = toggleAnimation;
window.changeMapColor = changeMapColor;
