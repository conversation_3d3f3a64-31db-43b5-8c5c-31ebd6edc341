<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IndexedDB 示例 - 用户管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.delete {
            background-color: #dc3545;
        }
        button.delete:hover {
            background-color: #c82333;
        }
        .user-list {
            margin-top: 20px;
        }
        .user-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .user-actions {
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>IndexedDB 示例 - 用户管理系统</h1>
    
    <div class="container">
        <h2>添加用户</h2>
        <form id="userForm">
            <div class="form-group">
                <label for="name">姓名:</label>
                <input type="text" id="name" required>
            </div>
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="age">年龄:</label>
                <input type="number" id="age" min="1" max="120" required>
            </div>
            <div class="form-group">
                <label for="department">部门:</label>
                <select id="department" required>
                    <option value="">请选择部门</option>
                    <option value="技术部">技术部</option>
                    <option value="市场部">市场部</option>
                    <option value="人事部">人事部</option>
                    <option value="财务部">财务部</option>
                </select>
            </div>
            <button type="submit">添加用户</button>
        </form>
    </div>

    <div class="container">
        <h2>搜索用户</h2>
        <div class="form-group">
            <label for="searchInput">按姓名或邮箱搜索:</label>
            <input type="text" id="searchInput" placeholder="输入姓名或邮箱...">
        </div>
        <button onclick="searchUsers()">搜索</button>
        <button onclick="loadAllUsers()">显示所有用户</button>
        <button onclick="clearDatabase()" class="delete">清空数据库</button>
    </div>

    <div id="status"></div>

    <div class="container">
        <h2>用户列表</h2>
        <div id="userList" class="user-list"></div>
    </div>

    <script>
        // IndexedDB 数据库配置
        const DB_NAME = 'UserManagementDB';
        const DB_VERSION = 1;
        const STORE_NAME = 'users';
        
        let db;

        // 初始化数据库
        function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);
                
                request.onerror = () => {
                    reject('数据库打开失败');
                };
                
                request.onsuccess = (event) => {
                    db = event.target.result;
                    showStatus('数据库连接成功', 'success');
                    resolve(db);
                };
                
                // 数据库升级时创建对象存储
                request.onupgradeneeded = (event) => {
                    db = event.target.result;
                    
                    // 创建用户对象存储
                    const objectStore = db.createObjectStore(STORE_NAME, {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    
                    // 创建索引以支持搜索
                    objectStore.createIndex('name', 'name', { unique: false });
                    objectStore.createIndex('email', 'email', { unique: true });
                    objectStore.createIndex('department', 'department', { unique: false });
                    
                    console.log('数据库结构创建完成');
                };
            });
        }

        // 添加用户
        function addUser(userData) {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const objectStore = transaction.objectStore(STORE_NAME);
                
                // 添加创建时间
                userData.createdAt = new Date().toISOString();
                
                const request = objectStore.add(userData);
                
                request.onsuccess = () => {
                    resolve(request.result);
                };
                
                request.onerror = () => {
                    reject('添加用户失败: ' + request.error);
                };
            });
        }

        // 获取所有用户
        function getAllUsers() {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const objectStore = transaction.objectStore(STORE_NAME);
                const request = objectStore.getAll();
                
                request.onsuccess = () => {
                    resolve(request.result);
                };
                
                request.onerror = () => {
                    reject('获取用户列表失败');
                };
            });
        }

        // 根据ID删除用户
        function deleteUser(id) {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const objectStore = transaction.objectStore(STORE_NAME);
                const request = objectStore.delete(id);
                
                request.onsuccess = () => {
                    resolve();
                };
                
                request.onerror = () => {
                    reject('删除用户失败');
                };
            });
        }

        // 搜索用户
        function searchUsersByName(searchTerm) {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const objectStore = transaction.objectStore(STORE_NAME);
                const results = [];
                
                const request = objectStore.openCursor();
                
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        const user = cursor.value;
                        if (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            user.email.toLowerCase().includes(searchTerm.toLowerCase())) {
                            results.push(user);
                        }
                        cursor.continue();
                    } else {
                        resolve(results);
                    }
                };
                
                request.onerror = () => {
                    reject('搜索失败');
                };
            });
        }

        // 清空数据库
        function clearAllUsers() {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const objectStore = transaction.objectStore(STORE_NAME);
                const request = objectStore.clear();
                
                request.onsuccess = () => {
                    resolve();
                };
                
                request.onerror = () => {
                    reject('清空数据库失败');
                };
            });
        }

        // 显示状态消息
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        // 渲染用户列表
        function renderUsers(users) {
            const userList = document.getElementById('userList');
            
            if (users.length === 0) {
                userList.innerHTML = '<p>暂无用户数据</p>';
                return;
            }
            
            userList.innerHTML = users.map(user => `
                <div class="user-item">
                    <h3>${user.name}</h3>
                    <p><strong>邮箱:</strong> ${user.email}</p>
                    <p><strong>年龄:</strong> ${user.age}</p>
                    <p><strong>部门:</strong> ${user.department}</p>
                    <p><strong>创建时间:</strong> ${new Date(user.createdAt).toLocaleString()}</p>
                    <div class="user-actions">
                        <button onclick="deleteUserById(${user.id})" class="delete">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 表单提交处理
        document.getElementById('userForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                age: parseInt(document.getElementById('age').value),
                department: document.getElementById('department').value
            };
            
            try {
                await addUser(formData);
                showStatus('用户添加成功!', 'success');
                document.getElementById('userForm').reset();
                loadAllUsers(); // 刷新用户列表
            } catch (error) {
                showStatus(error, 'error');
            }
        });

        // 加载所有用户
        async function loadAllUsers() {
            try {
                const users = await getAllUsers();
                renderUsers(users);
            } catch (error) {
                showStatus(error, 'error');
            }
        }

        // 搜索用户
        async function searchUsers() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            
            if (!searchTerm) {
                showStatus('请输入搜索关键词', 'error');
                return;
            }
            
            try {
                const users = await searchUsersByName(searchTerm);
                renderUsers(users);
                showStatus(`找到 ${users.length} 个匹配的用户`, 'success');
            } catch (error) {
                showStatus(error, 'error');
            }
        }

        // 删除用户
        async function deleteUserById(id) {
            if (!confirm('确定要删除这个用户吗？')) {
                return;
            }
            
            try {
                await deleteUser(id);
                showStatus('用户删除成功!', 'success');
                loadAllUsers(); // 刷新用户列表
            } catch (error) {
                showStatus(error, 'error');
            }
        }

        // 清空数据库
        async function clearDatabase() {
            if (!confirm('确定要清空所有用户数据吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                await clearAllUsers();
                showStatus('数据库已清空!', 'success');
                loadAllUsers(); // 刷新用户列表
            } catch (error) {
                showStatus(error, 'error');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            try {
                await initDB();
                loadAllUsers();
            } catch (error) {
                showStatus('数据库初始化失败: ' + error, 'error');
            }
        });
    </script>
</body>
</html>
