<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL中国地图</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #mapCanvas {
            display: block;
            cursor: grab;
        }
        
        #mapCanvas:active {
            cursor: grabbing;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            color: white;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .control-group input[type="range"] {
            width: 150px;
        }
        
        .control-group button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        .control-group button:hover {
            background: #45a049;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 8px;
            color: white;
            font-size: 12px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="mapCanvas"></canvas>
        
        <div id="controls">
            <div class="control-group">
                <label>缩放级别</label>
                <input type="range" id="zoomSlider" min="0.5" max="8" step="0.1" value="2">
                <span id="zoomValue">2.0</span>
            </div>
            
            <div class="control-group">
                <label>地图颜色</label>
                <button onclick="changeMapColor('blue')">蓝色</button>
                <button onclick="changeMapColor('green')">绿色</button>
                <button onclick="changeMapColor('red')">红色</button>
                <button onclick="changeMapColor('purple')">紫色</button>
                <button onclick="changeMapColor('orange')">橙色</button>
                <button onclick="changeMapColor('yellow')">黄色</button>
            </div>
            
            <div class="control-group">
                <button onclick="resetView()">重置视图</button>
                <button onclick="toggleWireframe()">线框模式</button>
                <button onclick="toggleAnimation()">动画效果</button>
            </div>
        </div>
        
        <div id="info">
            <div>鼠标拖拽：平移地图</div>
            <div>滚轮：缩放地图</div>
            <div>双击：重置视图</div>
            <div>快捷键：R-重置 W-线框 A-动画 1-6-颜色</div>
            <div id="fps">FPS: --</div>
            <div id="provinceName" style="color: #4CAF50; font-weight: bold; margin-top: 5px;"></div>
        </div>
        
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载中国地图数据...</div>
        </div>
    </div>

    <script src="webgl-map-engine.js"></script>
    <script src="china-map-data.js"></script>
    <script src="main.js"></script>
</body>
</html>
