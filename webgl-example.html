<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL 示例 - 3D 旋转立方体</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 2px solid #333;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .control-group {
            margin: 15px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            text-align: right;
            margin-right: 10px;
            font-weight: bold;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        input[type="color"] {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .fps {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 WebGL 3D 立方体演示</h1>
        
        <div class="info">
            <strong>WebGL 功能展示:</strong> 这个示例展示了 WebGL 的核心功能，包括 3D 渲染、着色器编程、矩阵变换、纹理映射和实时动画。
        </div>

        <canvas id="webglCanvas" width="800" height="600"></canvas>
        <div class="fps" id="fps">FPS: 0</div>

        <div class="controls">
            <div class="control-group">
                <label>旋转速度:</label>
                <input type="range" id="rotationSpeed" min="0" max="5" step="0.1" value="1">
                <span id="speedValue">1.0</span>
            </div>
            
            <div class="control-group">
                <label>立方体颜色:</label>
                <input type="color" id="cubeColor" value="#ff6b6b">
            </div>
            
            <div class="control-group">
                <label>背景颜色:</label>
                <input type="color" id="bgColor" value="#4ecdc4">
            </div>
            
            <div class="control-group">
                <button onclick="toggleAnimation()">暂停/继续</button>
                <button onclick="resetRotation()">重置旋转</button>
                <button onclick="toggleWireframe()">线框模式</button>
            </div>
        </div>
    </div>

    <script>
        // WebGL 上下文和变量
        let gl;
        let shaderProgram;
        let cubeBuffer;
        let indexBuffer;
        let rotationX = 0;
        let rotationY = 0;
        let animationId;
        let isAnimating = true;
        let isWireframe = false;
        let lastTime = 0;
        let frameCount = 0;
        let fpsTime = 0;

        // 顶点着色器源码
        const vertexShaderSource = `
            attribute vec3 aVertexPosition;
            attribute vec3 aVertexColor;
            
            uniform mat4 uModelViewMatrix;
            uniform mat4 uProjectionMatrix;
            
            varying vec3 vColor;
            
            void main(void) {
                gl_Position = uProjectionMatrix * uModelViewMatrix * vec4(aVertexPosition, 1.0);
                vColor = aVertexColor;
            }
        `;

        // 片段着色器源码
        const fragmentShaderSource = `
            precision mediump float;
            varying vec3 vColor;
            uniform vec3 uColor;
            
            void main(void) {
                gl_FragColor = vec4(vColor * uColor, 1.0);
            }
        `;

        // 立方体顶点数据 (位置 + 颜色)
        const cubeVertices = [
            // 前面 (红色)
            -1.0, -1.0,  1.0,  1.0, 0.0, 0.0,
             1.0, -1.0,  1.0,  1.0, 0.0, 0.0,
             1.0,  1.0,  1.0,  1.0, 0.0, 0.0,
            -1.0,  1.0,  1.0,  1.0, 0.0, 0.0,
            
            // 后面 (绿色)
            -1.0, -1.0, -1.0,  0.0, 1.0, 0.0,
            -1.0,  1.0, -1.0,  0.0, 1.0, 0.0,
             1.0,  1.0, -1.0,  0.0, 1.0, 0.0,
             1.0, -1.0, -1.0,  0.0, 1.0, 0.0,
            
            // 顶面 (蓝色)
            -1.0,  1.0, -1.0,  0.0, 0.0, 1.0,
            -1.0,  1.0,  1.0,  0.0, 0.0, 1.0,
             1.0,  1.0,  1.0,  0.0, 0.0, 1.0,
             1.0,  1.0, -1.0,  0.0, 0.0, 1.0,
            
            // 底面 (黄色)
            -1.0, -1.0, -1.0,  1.0, 1.0, 0.0,
             1.0, -1.0, -1.0,  1.0, 1.0, 0.0,
             1.0, -1.0,  1.0,  1.0, 1.0, 0.0,
            -1.0, -1.0,  1.0,  1.0, 1.0, 0.0,
            
            // 右面 (紫色)
             1.0, -1.0, -1.0,  1.0, 0.0, 1.0,
             1.0,  1.0, -1.0,  1.0, 0.0, 1.0,
             1.0,  1.0,  1.0,  1.0, 0.0, 1.0,
             1.0, -1.0,  1.0,  1.0, 0.0, 1.0,
            
            // 左面 (青色)
            -1.0, -1.0, -1.0,  0.0, 1.0, 1.0,
            -1.0, -1.0,  1.0,  0.0, 1.0, 1.0,
            -1.0,  1.0,  1.0,  0.0, 1.0, 1.0,
            -1.0,  1.0, -1.0,  0.0, 1.0, 1.0,
        ];

        // 立方体面的索引
        const cubeIndices = [
            0,  1,  2,    0,  2,  3,    // 前面
            4,  5,  6,    4,  6,  7,    // 后面
            8,  9,  10,   8,  10, 11,   // 顶面
            12, 13, 14,   12, 14, 15,   // 底面
            16, 17, 18,   16, 18, 19,   // 右面
            20, 21, 22,   20, 22, 23,   // 左面
        ];

        // 初始化 WebGL
        function initWebGL() {
            const canvas = document.getElementById('webglCanvas');
            gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                alert('您的浏览器不支持 WebGL!');
                return false;
            }
            
            // 设置视口
            gl.viewport(0, 0, canvas.width, canvas.height);
            
            // 启用深度测试
            gl.enable(gl.DEPTH_TEST);
            gl.depthFunc(gl.LEQUAL);
            
            return true;
        }

        // 创建着色器
        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            
            return shader;
        }

        // 初始化着色器程序
        function initShaders() {
            const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
            const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
            
            shaderProgram = gl.createProgram();
            gl.attachShader(shaderProgram, vertexShader);
            gl.attachShader(shaderProgram, fragmentShader);
            gl.linkProgram(shaderProgram);
            
            if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
                console.error('着色器程序链接失败:', gl.getProgramInfoLog(shaderProgram));
                return false;
            }
            
            gl.useProgram(shaderProgram);
            
            // 获取属性和统一变量位置
            shaderProgram.vertexPositionAttribute = gl.getAttribLocation(shaderProgram, 'aVertexPosition');
            shaderProgram.vertexColorAttribute = gl.getAttribLocation(shaderProgram, 'aVertexColor');
            shaderProgram.projectionMatrixUniform = gl.getUniformLocation(shaderProgram, 'uProjectionMatrix');
            shaderProgram.modelViewMatrixUniform = gl.getUniformLocation(shaderProgram, 'uModelViewMatrix');
            shaderProgram.colorUniform = gl.getUniformLocation(shaderProgram, 'uColor');
            
            gl.enableVertexAttribArray(shaderProgram.vertexPositionAttribute);
            gl.enableVertexAttribArray(shaderProgram.vertexColorAttribute);
            
            return true;
        }

        // 初始化缓冲区
        function initBuffers() {
            // 创建顶点缓冲区
            cubeBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, cubeBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(cubeVertices), gl.STATIC_DRAW);
            
            // 创建索引缓冲区
            indexBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(cubeIndices), gl.STATIC_DRAW);
        }

        // 矩阵工具函数
        function createProjectionMatrix(fov, aspect, near, far) {
            const f = Math.tan(Math.PI * 0.5 - 0.5 * fov);
            const rangeInv = 1.0 / (near - far);
            
            return [
                f / aspect, 0, 0, 0,
                0, f, 0, 0,
                0, 0, (near + far) * rangeInv, -1,
                0, 0, near * far * rangeInv * 2, 0
            ];
        }

        function createModelViewMatrix(tx, ty, tz, rx, ry, rz) {
            const cosRx = Math.cos(rx), sinRx = Math.sin(rx);
            const cosRy = Math.cos(ry), sinRy = Math.sin(ry);
            const cosRz = Math.cos(rz), sinRz = Math.sin(rz);
            
            return [
                cosRy * cosRz, -cosRy * sinRz, sinRy, 0,
                cosRx * sinRz + sinRx * sinRy * cosRz, cosRx * cosRz - sinRx * sinRy * sinRz, -sinRx * cosRy, 0,
                sinRx * sinRz - cosRx * sinRy * cosRz, sinRx * cosRz + cosRx * sinRy * sinRz, cosRx * cosRy, 0,
                tx, ty, tz, 1
            ];
        }

        // 渲染场景
        function drawScene() {
            // 获取背景颜色
            const bgColor = hexToRgb(document.getElementById('bgColor').value);
            gl.clearColor(bgColor.r / 255, bgColor.g / 255, bgColor.b / 255, 1.0);
            gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
            
            // 设置投影矩阵
            const projectionMatrix = createProjectionMatrix(45 * Math.PI / 180, 800 / 600, 0.1, 100.0);
            gl.uniformMatrix4fv(shaderProgram.projectionMatrixUniform, false, projectionMatrix);
            
            // 设置模型视图矩阵
            const modelViewMatrix = createModelViewMatrix(0, 0, -6, rotationX, rotationY, 0);
            gl.uniformMatrix4fv(shaderProgram.modelViewMatrixUniform, false, modelViewMatrix);
            
            // 设置立方体颜色
            const cubeColor = hexToRgb(document.getElementById('cubeColor').value);
            gl.uniform3f(shaderProgram.colorUniform, cubeColor.r / 255, cubeColor.g / 255, cubeColor.b / 255);
            
            // 绑定缓冲区
            gl.bindBuffer(gl.ARRAY_BUFFER, cubeBuffer);
            gl.vertexAttribPointer(shaderProgram.vertexPositionAttribute, 3, gl.FLOAT, false, 24, 0);
            gl.vertexAttribPointer(shaderProgram.vertexColorAttribute, 3, gl.FLOAT, false, 24, 12);
            
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
            
            // 绘制立方体
            if (isWireframe) {
                for (let i = 0; i < cubeIndices.length; i += 3) {
                    gl.drawElements(gl.LINE_LOOP, 3, gl.UNSIGNED_SHORT, i * 2);
                }
            } else {
                gl.drawElements(gl.TRIANGLES, cubeIndices.length, gl.UNSIGNED_SHORT, 0);
            }
        }

        // 动画循环
        function animate(currentTime) {
            if (isAnimating) {
                const deltaTime = currentTime - lastTime;
                const speed = parseFloat(document.getElementById('rotationSpeed').value);
                
                rotationX += deltaTime * 0.001 * speed;
                rotationY += deltaTime * 0.0015 * speed;
                
                // 计算 FPS
                frameCount++;
                fpsTime += deltaTime;
                if (fpsTime >= 1000) {
                    document.getElementById('fps').textContent = `FPS: ${Math.round(frameCount * 1000 / fpsTime)}`;
                    frameCount = 0;
                    fpsTime = 0;
                }
            }
            
            lastTime = currentTime;
            drawScene();
            animationId = requestAnimationFrame(animate);
        }

        // 工具函数
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        // 控制函数
        function toggleAnimation() {
            isAnimating = !isAnimating;
        }

        function resetRotation() {
            rotationX = 0;
            rotationY = 0;
        }

        function toggleWireframe() {
            isWireframe = !isWireframe;
        }

        // 事件监听器
        document.getElementById('rotationSpeed').addEventListener('input', function(e) {
            document.getElementById('speedValue').textContent = e.target.value;
        });

        // 初始化
        window.addEventListener('load', function() {
            if (initWebGL()) {
                if (initShaders()) {
                    initBuffers();
                    requestAnimationFrame(animate);
                }
            }
        });
    </script>
</body>
</html>
