<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL 飞机大战</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Courier New', monospace;
            overflow: hidden;
            user-select: none;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .score-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #00ff00;
            font-size: 18px;
            text-shadow: 0 0 10px #00ff00;
        }
        
        .health-bar {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 200px;
            height: 20px;
            background: rgba(255, 0, 0, 0.3);
            border: 2px solid #ff0000;
            border-radius: 10px;
        }
        
        .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ffff00, #00ff00);
            border-radius: 8px;
            transition: width 0.3s ease;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #ff0000;
            font-size: 48px;
            text-shadow: 0 0 20px #ff0000;
            display: none;
            pointer-events: all;
        }
        
        .restart-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 20px;
            border-radius: 10px;
            cursor: pointer;
            margin-top: 20px;
            text-shadow: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0,0,0,0.4);
        }
        
        .instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #ffffff;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .crosshair {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid #00ff00;
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px #00ff00;
        }

        .power-ups {
            position: absolute;
            top: 80px;
            left: 20px;
            color: #ffff00;
            font-size: 14px;
        }

        .mini-map {
            position: absolute;
            top: 20px;
            right: 250px;
            width: 150px;
            height: 100px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff00;
            border-radius: 5px;
        }

        .weapon-indicator {
            position: absolute;
            bottom: 80px;
            right: 20px;
            color: #00ffff;
            font-size: 16px;
            text-align: right;
        }

        .combo-display {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffff00;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 10px #ffff00;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pause-menu {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            color: white;
            display: none;
            pointer-events: all;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas"></canvas>
    
    <div class="ui-overlay">
        <div class="score-panel">
            <div>得分: <span id="score">0</span></div>
            <div>等级: <span id="level">1</span></div>
            <div>敌机: <span id="enemies">0</span></div>
        </div>
        
        <div class="health-bar">
            <div class="health-fill" id="healthFill" style="width: 100%"></div>
        </div>
        
        <div class="game-over" id="gameOver">
            <div>游戏结束!</div>
            <div style="font-size: 24px; margin: 20px 0;">最终得分: <span id="finalScore">0</span></div>
            <button class="restart-btn" onclick="restartGame()">重新开始</button>
        </div>
        
        <div class="instructions">
            <div>🎮 控制: 鼠标移动 | 左键射击 | 空格键发射导弹</div>
            <div>🎯 目标: 击毁敌机获得分数，避免碰撞!</div>
        </div>
        
        <div class="crosshair" id="crosshair"></div>
    </div>

    <script>
        // WebGL 上下文和游戏变量
        let gl;
        let canvas;
        let shaderProgram;
        let gameState = {
            score: 0,
            level: 1,
            health: 100,
            isGameOver: false,
            isPaused: false
        };
        
        // 游戏对象
        let player = {
            x: 0, y: -3, z: 0,
            vx: 0, vy: 0,
            health: 100,
            lastShot: 0,
            shootCooldown: 100
        };
        
        let enemies = [];
        let bullets = [];
        let particles = [];
        let stars = [];
        
        // 输入状态
        let keys = {};
        let mouse = { x: 0, y: 0, down: false };
        
        // 时间和动画
        let lastTime = 0;
        let gameTime = 0;
        
        // 着色器源码
        const vertexShaderSource = `
            attribute vec3 aPosition;
            attribute vec3 aColor;
            
            uniform mat4 uProjectionMatrix;
            uniform mat4 uModelViewMatrix;
            uniform float uPointSize;
            
            varying vec3 vColor;
            
            void main() {
                gl_Position = uProjectionMatrix * uModelViewMatrix * vec4(aPosition, 1.0);
                gl_PointSize = uPointSize;
                vColor = aColor;
            }
        `;
        
        const fragmentShaderSource = `
            precision mediump float;
            varying vec3 vColor;
            
            void main() {
                gl_FragColor = vec4(vColor, 1.0);
            }
        `;
        
        // 初始化 WebGL
        function initWebGL() {
            canvas = document.getElementById('gameCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) {
                alert('您的浏览器不支持 WebGL!');
                return false;
            }
            
            gl.viewport(0, 0, canvas.width, canvas.height);
            gl.enable(gl.DEPTH_TEST);
            gl.enable(gl.BLEND);
            gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
            
            return true;
        }
        
        // 创建着色器
        function createShader(type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                return null;
            }
            return shader;
        }
        
        // 初始化着色器程序
        function initShaders() {
            const vertexShader = createShader(gl.VERTEX_SHADER, vertexShaderSource);
            const fragmentShader = createShader(gl.FRAGMENT_SHADER, fragmentShaderSource);
            
            shaderProgram = gl.createProgram();
            gl.attachShader(shaderProgram, vertexShader);
            gl.attachShader(shaderProgram, fragmentShader);
            gl.linkProgram(shaderProgram);
            
            if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
                console.error('着色器程序链接失败');
                return false;
            }
            
            gl.useProgram(shaderProgram);
            
            // 获取属性和统一变量位置
            shaderProgram.aPosition = gl.getAttribLocation(shaderProgram, 'aPosition');
            shaderProgram.aColor = gl.getAttribLocation(shaderProgram, 'aColor');
            shaderProgram.uProjectionMatrix = gl.getUniformLocation(shaderProgram, 'uProjectionMatrix');
            shaderProgram.uModelViewMatrix = gl.getUniformLocation(shaderProgram, 'uModelViewMatrix');
            shaderProgram.uPointSize = gl.getUniformLocation(shaderProgram, 'uPointSize');
            
            return true;
        }
        
        // 创建投影矩阵
        function createProjectionMatrix() {
            const aspect = canvas.width / canvas.height;
            const fov = Math.PI / 4;
            const near = 0.1;
            const far = 100.0;
            
            const f = Math.tan(Math.PI * 0.5 - 0.5 * fov);
            const rangeInv = 1.0 / (near - far);
            
            return new Float32Array([
                f / aspect, 0, 0, 0,
                0, f, 0, 0,
                0, 0, (near + far) * rangeInv, -1,
                0, 0, near * far * rangeInv * 2, 0
            ]);
        }
        
        // 创建模型视图矩阵
        function createModelViewMatrix(x, y, z) {
            return new Float32Array([
                1, 0, 0, 0,
                0, 1, 0, 0,
                0, 0, 1, 0,
                x, y, z, 1
            ]);
        }
        
        // 绘制对象
        function drawObject(vertices, colors, mode = gl.TRIANGLES, pointSize = 1.0) {
            const positionBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(vertices), gl.STATIC_DRAW);
            gl.vertexAttribPointer(shaderProgram.aPosition, 3, gl.FLOAT, false, 0, 0);
            gl.enableVertexAttribArray(shaderProgram.aPosition);
            
            const colorBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(colors), gl.STATIC_DRAW);
            gl.vertexAttribPointer(shaderProgram.aColor, 3, gl.FLOAT, false, 0, 0);
            gl.enableVertexAttribArray(shaderProgram.aColor);
            
            gl.uniform1f(shaderProgram.uPointSize, pointSize);
            gl.drawArrays(mode, 0, vertices.length / 3);
        }
        
        // 初始化游戏对象
        function initGame() {
            // 创建星空背景
            for (let i = 0; i < 200; i++) {
                stars.push({
                    x: (Math.random() - 0.5) * 20,
                    y: (Math.random() - 0.5) * 20,
                    z: Math.random() * -50 - 10,
                    brightness: Math.random()
                });
            }
            
            // 重置游戏状态
            gameState = {
                score: 0,
                level: 1,
                health: 100,
                isGameOver: false,
                isPaused: false
            };
            
            player = {
                x: 0, y: -3, z: 0,
                vx: 0, vy: 0,
                health: 100,
                lastShot: 0,
                shootCooldown: 100
            };
            
            enemies = [];
            bullets = [];
            particles = [];
        }
        
        // 生成敌机
        function spawnEnemy() {
            if (enemies.length < 5 + gameState.level) {
                enemies.push({
                    x: (Math.random() - 0.5) * 8,
                    y: Math.random() * 3 + 5,
                    z: 0,
                    vx: (Math.random() - 0.5) * 0.02,
                    vy: -0.01 - Math.random() * 0.02,
                    health: 1 + Math.floor(gameState.level / 3),
                    type: Math.random() > 0.8 ? 'boss' : 'normal',
                    shootTimer: 0
                });
            }
        }
        
        // 发射子弹
        function shootBullet(x, y, vx, vy, isPlayer = true) {
            bullets.push({
                x: x,
                y: y,
                z: 0,
                vx: vx,
                vy: vy,
                isPlayer: isPlayer,
                life: 100
            });
        }
        
        // 创建粒子效果
        function createExplosion(x, y, count = 10) {
            for (let i = 0; i < count; i++) {
                particles.push({
                    x: x + (Math.random() - 0.5) * 0.5,
                    y: y + (Math.random() - 0.5) * 0.5,
                    z: 0,
                    vx: (Math.random() - 0.5) * 0.1,
                    vy: (Math.random() - 0.5) * 0.1,
                    life: 30 + Math.random() * 20,
                    maxLife: 50,
                    color: [Math.random(), Math.random() * 0.5, 0]
                });
            }
        }
        
        // 碰撞检测
        function checkCollision(obj1, obj2, radius = 0.3) {
            const dx = obj1.x - obj2.x;
            const dy = obj1.y - obj2.y;
            return Math.sqrt(dx * dx + dy * dy) < radius;
        }
        
        // 更新游戏逻辑
        function updateGame(deltaTime) {
            if (gameState.isGameOver || gameState.isPaused) return;
            
            gameTime += deltaTime;
            
            // 更新玩家位置
            const mouseX = (mouse.x / canvas.width - 0.5) * 8;
            const mouseY = -(mouse.y / canvas.height - 0.5) * 6;
            
            player.x += (mouseX - player.x) * 0.1;
            player.y += (mouseY - player.y) * 0.1;
            
            // 限制玩家移动范围
            player.x = Math.max(-4, Math.min(4, player.x));
            player.y = Math.max(-4, Math.min(2, player.y));
            
            // 自动射击
            if (gameTime - player.lastShot > player.shootCooldown) {
                shootBullet(player.x, player.y + 0.3, 0, 0.15, true);
                player.lastShot = gameTime;
            }
            
            // 生成敌机
            if (Math.random() < 0.02 + gameState.level * 0.005) {
                spawnEnemy();
            }
            
            // 更新敌机
            enemies.forEach((enemy, enemyIndex) => {
                enemy.x += enemy.vx;
                enemy.y += enemy.vy;
                
                // 敌机射击
                enemy.shootTimer += deltaTime;
                if (enemy.shootTimer > 1000 + Math.random() * 2000) {
                    shootBullet(enemy.x, enemy.y - 0.2, 0, -0.1, false);
                    enemy.shootTimer = 0;
                }
                
                // 移除超出屏幕的敌机
                if (enemy.y < -6) {
                    enemies.splice(enemyIndex, 1);
                }
            });
            
            // 更新子弹
            bullets.forEach((bullet, bulletIndex) => {
                bullet.x += bullet.vx;
                bullet.y += bullet.vy;
                bullet.life--;
                
                // 移除生命值耗尽的子弹
                if (bullet.life <= 0 || bullet.y > 6 || bullet.y < -6) {
                    bullets.splice(bulletIndex, 1);
                    return;
                }
                
                // 子弹碰撞检测
                if (bullet.isPlayer) {
                    // 玩家子弹击中敌机
                    enemies.forEach((enemy, enemyIndex) => {
                        if (checkCollision(bullet, enemy)) {
                            enemy.health--;
                            bullets.splice(bulletIndex, 1);
                            createExplosion(enemy.x, enemy.y);
                            
                            if (enemy.health <= 0) {
                                gameState.score += enemy.type === 'boss' ? 100 : 50;
                                enemies.splice(enemyIndex, 1);
                                createExplosion(enemy.x, enemy.y, 15);
                            }
                        }
                    });
                } else {
                    // 敌机子弹击中玩家
                    if (checkCollision(bullet, player)) {
                        player.health -= 10;
                        gameState.health = player.health;
                        bullets.splice(bulletIndex, 1);
                        createExplosion(player.x, player.y, 5);
                        
                        if (player.health <= 0) {
                            gameState.isGameOver = true;
                        }
                    }
                }
            });
            
            // 玩家与敌机碰撞
            enemies.forEach((enemy, enemyIndex) => {
                if (checkCollision(player, enemy)) {
                    player.health -= 20;
                    gameState.health = player.health;
                    enemies.splice(enemyIndex, 1);
                    createExplosion(enemy.x, enemy.y, 10);
                    
                    if (player.health <= 0) {
                        gameState.isGameOver = true;
                    }
                }
            });
            
            // 更新粒子
            particles.forEach((particle, particleIndex) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                
                if (particle.life <= 0) {
                    particles.splice(particleIndex, 1);
                }
            });
            
            // 更新星空
            stars.forEach(star => {
                star.z += 0.1;
                if (star.z > 0) {
                    star.z = -50;
                    star.x = (Math.random() - 0.5) * 20;
                    star.y = (Math.random() - 0.5) * 20;
                }
            });
            
            // 升级检测
            const newLevel = Math.floor(gameState.score / 500) + 1;
            if (newLevel > gameState.level) {
                gameState.level = newLevel;
                player.shootCooldown = Math.max(50, player.shootCooldown - 10);
            }
            
            // 更新UI
            updateUI();
        }
        
        // 渲染游戏
        function render() {
            gl.clearColor(0.0, 0.0, 0.1, 1.0);
            gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
            
            const projectionMatrix = createProjectionMatrix();
            gl.uniformMatrix4fv(shaderProgram.uProjectionMatrix, false, projectionMatrix);
            
            // 绘制星空
            stars.forEach(star => {
                const modelViewMatrix = createModelViewMatrix(star.x, star.y, star.z);
                gl.uniformMatrix4fv(shaderProgram.uModelViewMatrix, false, modelViewMatrix);
                
                const brightness = star.brightness * 0.8;
                drawObject([0, 0, 0], [brightness, brightness, brightness], gl.POINTS, 2.0);
            });
            
            if (!gameState.isGameOver) {
                // 绘制玩家
                const playerMatrix = createModelViewMatrix(player.x, player.y, player.z);
                gl.uniformMatrix4fv(shaderProgram.uModelViewMatrix, false, playerMatrix);
                
                const playerVertices = [
                    0, 0.3, 0,
                    -0.2, -0.2, 0,
                    0.2, -0.2, 0
                ];
                const playerColors = [
                    0, 1, 0,
                    0, 0.8, 0,
                    0, 0.8, 0
                ];
                drawObject(playerVertices, playerColors);
            }
            
            // 绘制敌机
            enemies.forEach(enemy => {
                const enemyMatrix = createModelViewMatrix(enemy.x, enemy.y, enemy.z);
                gl.uniformMatrix4fv(shaderProgram.uModelViewMatrix, false, enemyMatrix);
                
                const size = enemy.type === 'boss' ? 0.3 : 0.2;
                const enemyVertices = [
                    0, -size, 0,
                    -size, size, 0,
                    size, size, 0
                ];
                const enemyColors = enemy.type === 'boss' ? 
                    [1, 0.5, 0, 1, 0, 0, 1, 0, 0] : 
                    [1, 0, 0, 0.8, 0, 0, 0.8, 0, 0];
                drawObject(enemyVertices, enemyColors);
            });
            
            // 绘制子弹
            bullets.forEach(bullet => {
                const bulletMatrix = createModelViewMatrix(bullet.x, bullet.y, bullet.z);
                gl.uniformMatrix4fv(shaderProgram.uModelViewMatrix, false, bulletMatrix);
                
                const color = bullet.isPlayer ? [0, 1, 1] : [1, 1, 0];
                drawObject([0, 0, 0], color, gl.POINTS, 4.0);
            });
            
            // 绘制粒子
            particles.forEach(particle => {
                const particleMatrix = createModelViewMatrix(particle.x, particle.y, particle.z);
                gl.uniformMatrix4fv(shaderProgram.uModelViewMatrix, false, particleMatrix);
                
                const alpha = particle.life / particle.maxLife;
                const color = [
                    particle.color[0] * alpha,
                    particle.color[1] * alpha,
                    particle.color[2] * alpha
                ];
                drawObject([0, 0, 0], color, gl.POINTS, 3.0);
            });
        }
        
        // 更新UI
        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('level').textContent = gameState.level;
            document.getElementById('enemies').textContent = enemies.length;
            
            const healthPercent = Math.max(0, gameState.health);
            document.getElementById('healthFill').style.width = healthPercent + '%';
            
            if (gameState.isGameOver) {
                document.getElementById('finalScore').textContent = gameState.score;
                document.getElementById('gameOver').style.display = 'block';
            }
        }
        
        // 游戏主循环
        function gameLoop(currentTime) {
            const deltaTime = currentTime - lastTime;
            lastTime = currentTime;
            
            updateGame(deltaTime);
            render();
            
            requestAnimationFrame(gameLoop);
        }
        
        // 重启游戏
        function restartGame() {
            document.getElementById('gameOver').style.display = 'none';
            initGame();
        }
        
        // 事件监听
        function setupEventListeners() {
            // 鼠标移动
            canvas.addEventListener('mousemove', (e) => {
                mouse.x = e.clientX;
                mouse.y = e.clientY;
                
                // 更新准星位置
                const crosshair = document.getElementById('crosshair');
                crosshair.style.left = e.clientX + 'px';
                crosshair.style.top = e.clientY + 'px';
            });
            
            // 鼠标点击
            canvas.addEventListener('mousedown', (e) => {
                mouse.down = true;
            });
            
            canvas.addEventListener('mouseup', (e) => {
                mouse.down = false;
            });
            
            // 键盘事件
            document.addEventListener('keydown', (e) => {
                keys[e.code] = true;
                
                if (e.code === 'Space') {
                    e.preventDefault();
                    // 发射特殊武器
                    if (gameTime - player.lastShot > 200) {
                        shootBullet(player.x - 0.1, player.y + 0.3, -0.02, 0.2, true);
                        shootBullet(player.x + 0.1, player.y + 0.3, 0.02, 0.2, true);
                        player.lastShot = gameTime;
                    }
                }
            });
            
            document.addEventListener('keyup', (e) => {
                keys[e.code] = false;
            });
            
            // 窗口大小调整
            window.addEventListener('resize', () => {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                gl.viewport(0, 0, canvas.width, canvas.height);
            });
        }
        
        // 初始化游戏
        window.addEventListener('load', () => {
            if (initWebGL() && initShaders()) {
                initGame();
                setupEventListeners();
                requestAnimationFrame(gameLoop);
            }
        });
    </script>
</body>
</html>
