/**
 * WebGL中国地图渲染引擎
 */
class WebGLMapEngine {
    constructor(canvas) {
        this.canvas = canvas;
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.uniforms = {};
        this.attributes = {};
        
        // 变换矩阵
        this.viewMatrix = this.createIdentityMatrix();
        this.projectionMatrix = this.createIdentityMatrix();
        this.modelMatrix = this.createIdentityMatrix();
        
        // 交互状态
        this.isDragging = false;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.translateX = 0;
        this.translateY = 0;
        this.scale = 1.0;
        
        // 渲染设置
        this.mapColor = [0.2, 0.6, 1.0, 1.0]; // 默认蓝色
        this.wireframeMode = false;
        this.animationId = null;
        this.animationEnabled = true;

        // 性能监控
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 0;

        this.init();
    }
    
    init() {
        // 初始化WebGL上下文
        this.gl = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl');
        if (!this.gl) {
            throw new Error('WebGL不被支持');
        }
        
        // 设置画布大小
        this.resizeCanvas();
        
        // 创建着色器程序
        this.createShaderProgram();
        
        // 设置WebGL状态
        this.setupWebGL();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('WebGL地图引擎初始化完成');
    }
    
    resizeCanvas() {
        const displayWidth = this.canvas.clientWidth;
        const displayHeight = this.canvas.clientHeight;
        
        if (this.canvas.width !== displayWidth || this.canvas.height !== displayHeight) {
            this.canvas.width = displayWidth;
            this.canvas.height = displayHeight;
            this.gl.viewport(0, 0, displayWidth, displayHeight);
            this.updateProjectionMatrix();
        }
    }
    
    createShaderProgram() {
        // 顶点着色器源码
        const vertexShaderSource = `
            attribute vec2 a_position;
            uniform mat4 u_modelViewProjection;
            varying vec2 v_position;

            void main() {
                v_position = a_position;
                gl_Position = u_modelViewProjection * vec4(a_position, 0.0, 1.0);
            }
        `;

        // 片段着色器源码
        const fragmentShaderSource = `
            precision mediump float;
            uniform vec4 u_color;
            uniform float u_time;
            varying vec2 v_position;

            void main() {
                // 添加渐变效果
                float gradient = (v_position.y + 1.0) * 0.5;
                vec4 color1 = u_color;
                vec4 color2 = vec4(u_color.rgb * 0.7, u_color.a);
                vec4 finalColor = mix(color2, color1, gradient);

                // 添加微妙的动画效果
                float pulse = sin(u_time * 0.002) * 0.05 + 0.95;
                finalColor.rgb *= pulse;

                gl_FragColor = finalColor;
            }
        `;
        
        // 创建着色器
        const vertexShader = this.createShader(this.gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, fragmentShaderSource);
        
        // 创建程序
        this.program = this.gl.createProgram();
        this.gl.attachShader(this.program, vertexShader);
        this.gl.attachShader(this.program, fragmentShader);
        this.gl.linkProgram(this.program);
        
        if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
            throw new Error('着色器程序链接失败: ' + this.gl.getProgramInfoLog(this.program));
        }
        
        // 获取属性和uniform位置
        this.attributes.position = this.gl.getAttribLocation(this.program, 'a_position');
        this.uniforms.modelViewProjection = this.gl.getUniformLocation(this.program, 'u_modelViewProjection');
        this.uniforms.color = this.gl.getUniformLocation(this.program, 'u_color');
        this.uniforms.time = this.gl.getUniformLocation(this.program, 'u_time');
    }
    
    createShader(type, source) {
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);
        
        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            const error = this.gl.getShaderInfoLog(shader);
            this.gl.deleteShader(shader);
            throw new Error('着色器编译失败: ' + error);
        }
        
        return shader;
    }
    
    setupWebGL() {
        this.gl.clearColor(0.1, 0.1, 0.1, 1.0);
        this.gl.enable(this.gl.BLEND);
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);

        // 启用深度测试
        this.gl.enable(this.gl.DEPTH_TEST);
        this.gl.depthFunc(this.gl.LEQUAL);

        // 启用面剔除优化
        this.gl.enable(this.gl.CULL_FACE);
        this.gl.cullFace(this.gl.BACK);

        this.updateProjectionMatrix();
    }
    
    updateProjectionMatrix() {
        const aspect = this.canvas.width / this.canvas.height;
        this.projectionMatrix = this.createOrthographicMatrix(-aspect, aspect, -1, 1, -1, 1);
    }
    
    createIdentityMatrix() {
        return [
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ];
    }
    
    createOrthographicMatrix(left, right, bottom, top, near, far) {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        const nf = 1 / (near - far);
        
        return [
            -2 * lr, 0, 0, 0,
            0, -2 * bt, 0, 0,
            0, 0, 2 * nf, 0,
            (left + right) * lr, (top + bottom) * bt, (far + near) * nf, 1
        ];
    }
    
    multiplyMatrices(a, b) {
        const result = new Array(16);
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result[i * 4 + j] = 0;
                for (let k = 0; k < 4; k++) {
                    result[i * 4 + j] += a[i * 4 + k] * b[k * 4 + j];
                }
            }
        }
        return result;
    }
    
    createTranslationMatrix(x, y) {
        return [
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            x, y, 0, 1
        ];
    }
    
    createScaleMatrix(sx, sy) {
        return [
            sx, 0, 0, 0,
            0, sy, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ];
    }
    
    bindEvents() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.onWheel(e));
        this.canvas.addEventListener('dblclick', (e) => this.onDoubleClick(e));
        
        // 触摸事件（移动端支持）
        this.canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    onMouseDown(e) {
        this.isDragging = true;
        this.lastMouseX = e.clientX;
        this.lastMouseY = e.clientY;
    }
    
    onMouseMove(e) {
        if (!this.isDragging) return;
        
        const deltaX = (e.clientX - this.lastMouseX) / this.canvas.width * 2;
        const deltaY = -(e.clientY - this.lastMouseY) / this.canvas.height * 2;
        
        this.translateX += deltaX / this.scale;
        this.translateY += deltaY / this.scale;
        
        this.lastMouseX = e.clientX;
        this.lastMouseY = e.clientY;
    }
    
    onMouseUp() {
        this.isDragging = false;
    }
    
    onWheel(e) {
        e.preventDefault();
        
        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        this.scale *= zoomFactor;
        this.scale = Math.max(0.1, Math.min(5.0, this.scale));
        
        // 更新缩放滑块
        const zoomSlider = document.getElementById('zoomSlider');
        if (zoomSlider) {
            zoomSlider.value = this.scale;
            document.getElementById('zoomValue').textContent = this.scale.toFixed(1);
        }
    }
    
    onDoubleClick() {
        this.resetView();
    }
    
    // 触摸事件处理
    onTouchStart(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            this.onMouseDown({clientX: touch.clientX, clientY: touch.clientY});
        }
    }
    
    onTouchMove(e) {
        e.preventDefault();
        if (e.touches.length === 1 && this.isDragging) {
            const touch = e.touches[0];
            this.onMouseMove({clientX: touch.clientX, clientY: touch.clientY});
        }
    }
    
    onTouchEnd(e) {
        e.preventDefault();
        this.onMouseUp(e);
    }
    
    resetView() {
        this.translateX = 0;
        this.translateY = 0;
        this.scale = 1.0;
        
        // 更新UI
        const zoomSlider = document.getElementById('zoomSlider');
        if (zoomSlider) {
            zoomSlider.value = this.scale;
            document.getElementById('zoomValue').textContent = this.scale.toFixed(1);
        }
    }
    
    setMapColor(color) {
        this.mapColor = color;
    }

    toggleWireframe() {
        this.wireframeMode = !this.wireframeMode;
    }

    toggleAnimation() {
        this.animationEnabled = !this.animationEnabled;
        if (this.animationEnabled) {
            this.startAnimation();
        } else {
            this.stopAnimation();
            this.render(); // 静态渲染一次
        }
    }

    // 加载地图数据
    loadMapData(vertices, indices, borderVertices) {
        // 创建顶点缓冲区
        this.buffers.vertices = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.vertices);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(vertices), this.gl.STATIC_DRAW);

        // 创建索引缓冲区
        if (indices && indices.length > 0) {
            this.buffers.indices = this.gl.createBuffer();
            this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.indices);
            this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(indices), this.gl.STATIC_DRAW);
            this.indexCount = indices.length;
        } else {
            this.vertexCount = vertices.length / 2;
        }

        // 创建边界线缓冲区
        if (borderVertices && borderVertices.length > 0) {
            this.buffers.borderVertices = this.gl.createBuffer();
            this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.borderVertices);
            this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(borderVertices), this.gl.STATIC_DRAW);
            this.borderVertexCount = borderVertices.length / 2;
        }

        console.log('地图数据加载完成，顶点数:', vertices.length / 2);
        if (borderVertices) {
            console.log('边界线顶点数:', borderVertices.length / 2);
        }

        // 启动动画循环
        this.startAnimation();
    }

    // 启动动画循环
    startAnimation() {
        if (!this.animationEnabled) return;

        const animate = () => {
            if (this.animationEnabled) {
                this.render();
                this.animationId = requestAnimationFrame(animate);
            }
        };
        animate();
    }

    // 停止动画循环
    stopAnimation() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    // 渲染地图
    render() {
        if (!this.buffers.vertices) return;

        // 更新FPS计数
        this.updateFPS();

        this.resizeCanvas();

        // 清空画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // 使用着色器程序
        this.gl.useProgram(this.program);

        // 计算变换矩阵
        const translationMatrix = this.createTranslationMatrix(this.translateX, this.translateY);
        const scaleMatrix = this.createScaleMatrix(this.scale, this.scale);
        const modelMatrix = this.multiplyMatrices(translationMatrix, scaleMatrix);
        const mvpMatrix = this.multiplyMatrices(this.projectionMatrix, modelMatrix);

        // 设置uniform
        this.gl.uniformMatrix4fv(this.uniforms.modelViewProjection, false, mvpMatrix);
        this.gl.uniform4fv(this.uniforms.color, this.mapColor);
        this.gl.uniform1f(this.uniforms.time, Date.now());

        // 绑定顶点数据
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.vertices);
        this.gl.enableVertexAttribArray(this.attributes.position);
        this.gl.vertexAttribPointer(this.attributes.position, 2, this.gl.FLOAT, false, 0, 0);

        // 绘制地图填充
        if (!this.wireframeMode) {
            if (this.buffers.indices) {
                this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.indices);
                this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);
            } else {
                this.gl.drawArrays(this.gl.TRIANGLE_FAN, 0, this.vertexCount);
            }
        }

        // 绘制边界线
        if (this.buffers.borderVertices) {
            // 设置边界线颜色（更深的颜色）
            const borderColor = [
                this.mapColor[0] * 0.3,
                this.mapColor[1] * 0.3,
                this.mapColor[2] * 0.3,
                1.0
            ];
            this.gl.uniform4fv(this.uniforms.color, borderColor);

            // 绑定边界线顶点数据
            this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.borderVertices);
            this.gl.vertexAttribPointer(this.attributes.position, 2, this.gl.FLOAT, false, 0, 0);

            // 设置线宽
            this.gl.lineWidth(2.0);

            // 绘制边界线
            this.gl.drawArrays(this.gl.LINES, 0, this.borderVertexCount);
        } else if (this.wireframeMode) {
            // 线框模式
            const borderColor = [
                this.mapColor[0] * 0.8,
                this.mapColor[1] * 0.8,
                this.mapColor[2] * 0.8,
                1.0
            ];
            this.gl.uniform4fv(this.uniforms.color, borderColor);

            if (this.buffers.indices) {
                this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.indices);
                this.gl.drawElements(this.gl.LINES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);
            } else {
                this.gl.drawArrays(this.gl.LINE_LOOP, 0, this.vertexCount);
            }
        }
    }

    // 更新FPS计数
    updateFPS() {
        this.frameCount++;
        const currentTime = Date.now();

        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;

            // 更新FPS显示
            const fpsElement = document.getElementById('fps');
            if (fpsElement) {
                fpsElement.textContent = `FPS: ${this.fps}`;
            }
        }
    }

    // 获取画布坐标
    getCanvasCoordinates(clientX, clientY) {
        const rect = this.canvas.getBoundingClientRect();
        const x = ((clientX - rect.left) / rect.width) * 2 - 1;
        const y = -(((clientY - rect.top) / rect.height) * 2 - 1);
        return {x, y};
    }

    // 销毁资源
    destroy() {
        this.stopAnimation();

        if (this.buffers.vertices) {
            this.gl.deleteBuffer(this.buffers.vertices);
        }
        if (this.buffers.indices) {
            this.gl.deleteBuffer(this.buffers.indices);
        }
        if (this.buffers.borderVertices) {
            this.gl.deleteBuffer(this.buffers.borderVertices);
        }
        if (this.program) {
            this.gl.deleteProgram(this.program);
        }
    }
}
